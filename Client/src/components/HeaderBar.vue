<template>
  <div class="header-bar">
    <div class="header-content">
      <!-- 系统Logo和标题 -->
      <div class="header-left">
        <div class="logo">
          <el-icon size="24" color="#ffffff">
            <Monitor />
          </el-icon>
        </div>
        <h1 class="system-title">Web EMS 管理系统</h1>
      </div>
      
      <!-- 右侧功能区域 -->
      <div class="header-right">
        <!-- 搜索框 -->
        <div class="search-box">
          <el-input
            v-model="searchText"
            placeholder="搜索功能..."
            :prefix-icon="Search"
            clearable
            size="small"
            style="width: 200px"
          />
        </div>
        
        <!-- 用户信息 -->
        <div class="user-info">
          <el-dropdown>
            <div class="user-avatar">
              <el-avatar :size="32" :src="userAvatar">
                <el-icon><User /></el-icon>
              </el-avatar>
              <span class="username">管理员</span>
              <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item>个人设置</el-dropdown-item>
                <el-dropdown-item>修改密码</el-dropdown-item>
                <el-dropdown-item divided>退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Monitor, Search, User, ArrowDown } from '@element-plus/icons-vue'

// 响应式数据
const searchText = ref('')
const userAvatar = ref('')
</script>

<style scoped>
.header-bar {
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.header-content {
  height: 100%;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.system-title {
  color: #ffffff;
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.search-box {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  cursor: pointer;
  transition: all 0.3s ease;
}

.user-avatar:hover {
  background: rgba(255, 255, 255, 0.2);
}

.username {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
}

.dropdown-icon {
  color: #ffffff;
  font-size: 12px;
}

/* Element Plus 样式覆盖 */
:deep(.el-input__wrapper) {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 20px;
}

:deep(.el-input__inner) {
  color: #333;
}

:deep(.el-input__inner::placeholder) {
  color: #999;
}
</style>
