<template>
  <div class="sidebar-nav">
    <div class="sidebar-content">
      <!-- 顶部标识区域 -->
      <div class="sidebar-header">
        <div class="logo-area">
          <div class="logo-text">一体化平台</div>
          <div class="logo-subtext">管控</div>
        </div>
      </div>

      <!-- 导航菜单 -->
      <div class="nav-menu">
        <div
          v-for="item in menuItems"
          :key="item.id"
          class="nav-item"
          :class="{ active: item.active }"
          @click="handleMenuClick(item)"
        >
          <div class="nav-item-content">
            <!-- 图标 -->
            <div v-if="item.icon" class="nav-icon">
              <el-icon :size="18">
                <component :is="item.icon" />
              </el-icon>
            </div>

            <!-- 标题 -->
            <span class="nav-title">{{ item.title }}</span>

            <!-- 下拉箭头 - 只有有子菜单的项目才显示 -->
            <div v-if="item.children && item.children.length > 0" class="nav-arrow">
              <el-icon :size="14" class="arrow-icon" :class="{ expanded: item.expanded }">
                <ArrowRight />
              </el-icon>
            </div>
          </div>

          <!-- 子菜单（暂时空实现） -->
          <div v-if="item.children && item.expanded" class="sub-menu">
            <div
              v-for="child in item.children"
              :key="child.id"
              class="sub-nav-item"
              :class="{ active: child.active }"
              @click.stop="handleSubMenuClick(child)"
            >
              <span class="sub-nav-title">{{ child.title }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  House,
  DataAnalysis,
  Setting,
  User,
  Document,
  Monitor,
  ArrowRight
} from '@element-plus/icons-vue'
import { useUserStore, UserRole } from '@/stores/user'

// 用户状态管理
const userStore = useUserStore()

// 菜单项接口定义
interface MenuItem {
  id: string
  title: string
  icon?: any
  active?: boolean
  expanded?: boolean
  children?: MenuItem[]
}

// 超管菜单配置
const superAdminMenus: MenuItem[] = [
  {
    id: 'home',
    title: '首页',
    icon: House,
    active: true,
    expanded: false
  },
  {
    id: 'basic-data',
    title: '基础数据',
    icon: DataAnalysis,
    active: false,
    expanded: false,
    children: [
      { id: 'data-dict', title: '数据字典', active: false },
      { id: 'code-manage', title: '编码管理', active: false }
    ]
  },
  {
    id: 'customer-management',
    title: '客户管理',
    icon: User,
    active: false,
    expanded: false,
    children: [
      { id: 'customer-list', title: '客户列表', active: false },
      { id: 'customer-category', title: '客户分类', active: false }
    ]
  },
  {
    id: 'task-dispatch',
    title: '任务下达',
    icon: Document,
    active: false,
    expanded: false,
    children: [
      { id: 'task-create', title: '任务创建', active: false },
      { id: 'task-assign', title: '任务分配', active: false }
    ]
  },
  {
    id: 'production-management',
    title: '生产管理',
    icon: Setting,
    active: false,
    expanded: false,
    children: [
      { id: 'production-plan', title: '生产计划', active: false },
      { id: 'production-monitor', title: '生产监控', active: false }
    ]
  },
  {
    id: 'problem-management',
    title: '问题管理',
    icon: Monitor,
    active: false,
    expanded: false,
    children: [
      { id: 'problem-report', title: '问题上报', active: false },
      { id: 'problem-track', title: '问题跟踪', active: false }
    ]
  },
  {
    id: 'quality-control',
    title: '质量控制',
    icon: DataAnalysis,
    active: false,
    expanded: false,
    children: [
      { id: 'quality-check', title: '质量检查', active: false },
      { id: 'quality-report', title: '质量报告', active: false }
    ]
  },
  {
    id: 'delivery-management',
    title: '交货管理',
    icon: Document,
    active: false,
    expanded: false,
    children: [
      { id: 'delivery-plan', title: '交货计划', active: false },
      { id: 'delivery-track', title: '交货跟踪', active: false }
    ]
  },
  {
    id: 'system-management',
    title: '系统管理',
    icon: Setting,
    active: false,
    expanded: false,
    children: [
      { id: 'user-manage', title: '用户管理', active: false },
      { id: 'role-manage', title: '角色管理', active: false },
      { id: 'permission-manage', title: '权限管理', active: false }
    ]
  }
]

// 产品中心负责人菜单配置
const productCenterManagerMenus: MenuItem[] = [
  {
    id: 'home',
    title: '首页',
    icon: House,
    active: true,
    expanded: false
  },
  {
    id: 'basic-data',
    title: '基础数据',
    icon: DataAnalysis,
    active: false,
    expanded: false,
    children: [
      { id: 'data-dict', title: '数据字典', active: false },
      { id: 'code-manage', title: '编码管理', active: false }
    ]
  },
  {
    id: 'customer-management',
    title: '客户管理',
    icon: User,
    active: false,
    expanded: false,
    children: [
      { id: 'customer-list', title: '客户列表', active: false },
      { id: 'customer-category', title: '客户分类', active: false }
    ]
  },
  {
    id: 'task-dispatch',
    title: '任务下达',
    icon: Document,
    active: false,
    expanded: false,
    children: [
      { id: 'task-create', title: '任务创建', active: false },
      { id: 'task-assign', title: '任务分配', active: false }
    ]
  },
  {
    id: 'production-management',
    title: '生产管理',
    icon: Setting,
    active: false,
    expanded: false,
    children: [
      { id: 'production-plan', title: '生产计划', active: false },
      { id: 'production-monitor', title: '生产监控', active: false }
    ]
  },
  {
    id: 'problem-management',
    title: '问题管理',
    icon: Monitor,
    active: false,
    expanded: false,
    children: [
      { id: 'problem-report', title: '问题上报', active: false },
      { id: 'problem-track', title: '问题跟踪', active: false }
    ]
  },
  {
    id: 'quality-control',
    title: '质量照顾',
    icon: DataAnalysis,
    active: false,
    expanded: false,
    children: [
      { id: 'quality-check', title: '质量检查', active: false },
      { id: 'quality-report', title: '质量报告', active: false }
    ]
  },
  {
    id: 'delivery-management',
    title: '交货管理',
    icon: Document,
    active: false,
    expanded: false,
    children: [
      { id: 'delivery-plan', title: '交货计划', active: false },
      { id: 'delivery-track', title: '交货跟踪', active: false }
    ]
  }
]

// 生产人员菜单配置
const productionStaffMenus: MenuItem[] = [
  {
    id: 'home',
    title: '首页',
    icon: House,
    active: true,
    expanded: false
  },
  {
    id: 'production-management',
    title: '生产管理',
    icon: Setting,
    active: false,
    expanded: false,
    children: [
      { id: 'production-plan', title: '生产计划', active: false },
      { id: 'production-monitor', title: '生产监控', active: false }
    ]
  },
  {
    id: 'problem-management',
    title: '问题管理',
    icon: Monitor,
    active: false,
    expanded: false,
    children: [
      { id: 'problem-report', title: '问题上报', active: false },
      { id: 'problem-track', title: '问题跟踪', active: false }
    ]
  },
  {
    id: 'quality-control',
    title: '质量照顾',
    icon: DataAnalysis,
    active: false,
    expanded: false,
    children: [
      { id: 'quality-check', title: '质量检查', active: false },
      { id: 'quality-report', title: '质量报告', active: false }
    ]
  },
  {
    id: 'delivery-management',
    title: '交货管理',
    icon: Document,
    active: false,
    expanded: false,
    children: [
      { id: 'delivery-plan', title: '交货计划', active: false },
      { id: 'delivery-track', title: '交货跟踪', active: false }
    ]
  }
]

// 操作员菜单配置
const operatorMenus: MenuItem[] = [
  {
    id: 'home',
    title: '首页',
    icon: House,
    active: true,
    expanded: false
  },
  {
    id: 'production-management',
    title: '生产管理',
    icon: Setting,
    active: false,
    expanded: false,
    children: [
      { id: 'production-plan', title: '生产计划', active: false },
      { id: 'production-monitor', title: '生产监控', active: false }
    ]
  },
  {
    id: 'problem-management',
    title: '问题管理',
    icon: Monitor,
    active: false,
    expanded: false,
    children: [
      { id: 'problem-report', title: '问题上报', active: false },
      { id: 'problem-track', title: '问题跟踪', active: false }
    ]
  },
  {
    id: 'quality-control',
    title: '质量照顾',
    icon: DataAnalysis,
    active: false,
    expanded: false,
    children: [
      { id: 'quality-check', title: '质量检查', active: false },
      { id: 'quality-report', title: '质量报告', active: false }
    ]
  },
  {
    id: 'delivery-management',
    title: '交货管理',
    icon: Document,
    active: false,
    expanded: false,
    children: [
      { id: 'delivery-plan', title: '交货计划', active: false },
      { id: 'delivery-track', title: '交货跟踪', active: false }
    ]
  }
]

// 客户菜单配置
const customerMenus: MenuItem[] = [
  {
    id: 'home',
    title: '首页',
    icon: House,
    active: true,
    expanded: false
  },
  // 其他菜单待后续添加
]

// 根据角色获取菜单配置
const getMenusByRole = (role: UserRole): MenuItem[] => {
  switch (role) {
    case UserRole.SUPER_ADMIN:
      return superAdminMenus
    case UserRole.PRODUCT_CENTER_MANAGER:
      return productCenterManagerMenus
    case UserRole.PRODUCTION_STAFF:
      return productionStaffMenus
    case UserRole.OPERATOR:
      return operatorMenus
    case UserRole.CUSTOMER:
      return customerMenus
    default:
      return superAdminMenus
  }
}

// 计算属性：当前角色的菜单
const menuItems = computed(() => {
  return getMenusByRole(userStore.currentUser.role)
})

// 处理菜单点击
const handleMenuClick = (item: MenuItem) => {
  // 只有有子菜单的项目才切换展开状态
  if (item.children && item.children.length > 0) {
    item.expanded = !item.expanded
  }

  // 设置当前激活项
  const currentMenus = getMenusByRole(userStore.currentUser.role)
  currentMenus.forEach(menu => {
    menu.active = menu.id === item.id
    if (menu.children) {
      menu.children.forEach(child => {
        child.active = false
      })
    }
  })

  console.log('菜单点击:', item.title)
}

// 处理子菜单点击
const handleSubMenuClick = (child: MenuItem) => {
  // 设置子菜单激活状态
  const currentMenus = getMenusByRole(userStore.currentUser.role)
  currentMenus.forEach(menu => {
    menu.active = false
    if (menu.children) {
      menu.children.forEach(subItem => {
        subItem.active = subItem.id === child.id
      })
    }
  })

  console.log('子菜单点击:', child.title)
}
</script>

<style scoped>
.sidebar-nav {
  width: 250px;
  background: linear-gradient(180deg, #4a90e2 0%, #357abd 50%, #2c5aa0 100%);
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
}

.sidebar-content {
  padding: 0;
  height: 100%;
}

.sidebar-header {
  padding: 30px 20px;
  text-align: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 20px;
}

.logo-area {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50px;
  padding: 15px 25px;
  display: inline-block;
  backdrop-filter: blur(10px);
}

.logo-text {
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 2px;
}

.logo-subtext {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  opacity: 0.9;
}

.nav-menu {
  display: flex;
  flex-direction: column;
  gap: 2px;
  padding: 0 15px;
}

.nav-item {
  border-radius: 6px;
  transition: all 0.3s ease;
  cursor: pointer;
  margin-bottom: 2px;
}

.nav-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.nav-item.active {
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.nav-item-content {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  gap: 12px;
}

.nav-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  color: rgba(255, 255, 255, 0.8);
  transition: color 0.3s ease;
}

.nav-item.active .nav-icon {
  color: #ffffff;
}

.nav-title {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  transition: color 0.3s ease;
}

.nav-item.active .nav-title {
  color: #ffffff;
  font-weight: 600;
}

.nav-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.arrow-icon {
  color: rgba(255, 255, 255, 0.6);
  transition: all 0.3s ease;
}

.arrow-icon.expanded {
  transform: rotate(90deg);
  color: #ffffff;
}

.sub-menu {
  margin-left: 36px;
  margin-right: 16px;
  border-left: 1px solid rgba(255, 255, 255, 0.2);
  animation: slideDown 0.3s ease;
  padding-left: 12px;
}

.sub-nav-item {
  padding: 8px 12px;
  margin: 2px 0;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.sub-nav-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.sub-nav-item.active {
  background: rgba(255, 255, 255, 0.2);
}

.sub-nav-title {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.8);
  transition: color 0.3s ease;
}

.sub-nav-item.active .sub-nav-title {
  color: #ffffff;
  font-weight: 500;
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
  }
  to {
    opacity: 1;
    max-height: 200px;
  }
}
</style>
